import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { authMiddleware } from './middleware/auth';
import { auditMiddleware } from './middleware/audit';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import companyRoutes from './routes/companies';
import publicationRoutes from './routes/publications';
import mediaChannelRoutes from './routes/mediaChannels';
import dataParameterRoutes from './routes/dataParameters';
import dataEntryRoutes from './routes/dataEntries';
import editorialRoutes from './routes/editorials';
import swotAnalysisRoutes from './routes/swotAnalysis';
import dailyMentionRoutes from './routes/dailyMentions';
import analyticsRoutes from './routes/analytics';
import fileUploadRoutes from './routes/fileUploads';
import auditLogRoutes from './routes/auditLogs';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Media Monitoring Dashboard API',
      version: '1.0.0',
      description: 'API for Media Monitoring and Analytics Dashboard',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: `http://localhost:${PORT}`,
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./src/routes/*.ts', './src/controllers/*.ts'],
};

const specs = swaggerJsdoc(swaggerOptions);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve uploaded files
app.use('/uploads', express.static('uploads'));

// API Documentation
if (process.env.API_DOCS_ENABLED === 'true') {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));
}

// Root endpoint
app.get('/', (req, res) => {
  res.status(200).json({
    message: 'Media Monitoring Dashboard API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      docs: '/api-docs',
      api: '/api',
    },
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// Favicon handler
app.get('/favicon.ico', (req, res) => {
  res.status(204).end();
});

// Test endpoint
app.get('/test', (req, res) => {
  res.status(200).json({
    message: 'Server is working correctly!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: '1.0.0',
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', authMiddleware, auditMiddleware, userRoutes);
app.use('/api/companies', authMiddleware, auditMiddleware, companyRoutes);
app.use('/api/publications', authMiddleware, auditMiddleware, publicationRoutes);
app.use('/api/media-channels', authMiddleware, auditMiddleware, mediaChannelRoutes);
app.use('/api/data-parameters', authMiddleware, auditMiddleware, dataParameterRoutes);
app.use('/api/data-entries', authMiddleware, auditMiddleware, dataEntryRoutes);
app.use('/api/editorials', authMiddleware, auditMiddleware, editorialRoutes);
app.use('/api/swot-analysis', authMiddleware, auditMiddleware, swotAnalysisRoutes);
app.use('/api/daily-mentions', authMiddleware, auditMiddleware, dailyMentionRoutes);
app.use('/api/analytics', authMiddleware, analyticsRoutes);
app.use('/api/files', authMiddleware, auditMiddleware, fileUploadRoutes);
app.use('/api/audit-logs', authMiddleware, auditLogRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
});

export default app;
