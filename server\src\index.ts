import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import swaggerUi from 'swagger-ui-express';
import { specs } from './config/swagger';
import prisma from './lib/prisma';

import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { authMiddleware } from './middleware/auth';
import { auditMiddleware } from './middleware/audit';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import companyRoutes from './routes/companies';
import publicationRoutes from './routes/publications';
import mediaChannelRoutes from './routes/mediaChannels';
import dataParameterRoutes from './routes/dataParameters';
import dataEntryRoutes from './routes/dataEntries';
import editorialRoutes from './routes/editorials';
import swotAnalysisRoutes from './routes/swotAnalysis';
import dailyMentionRoutes from './routes/dailyMentions';
import analyticsRoutes from './routes/analytics';
import fileUploadRoutes from './routes/fileUploads';
import auditLogRoutes from './routes/auditLogs';
import exportRoutes from './routes/export';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Swagger configuration is imported from config/swagger.ts

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(morgan('combined'));
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:8080',
    'http://localhost:3000',
    'http://localhost:4173',
    process.env.CORS_ORIGIN
  ].filter(Boolean),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
  optionsSuccessStatus: 200, // Some legacy browsers choke on 204
}));

// Handle preflight requests explicitly
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve uploaded files
app.use('/uploads', express.static('uploads'));

// API Documentation
if (process.env.API_DOCS_ENABLED === 'true') {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));
}

// Root endpoint
app.get('/', (req, res) => {
  res.status(200).json({
    message: 'Media Monitoring Dashboard API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      docs: '/api-docs',
      api: '/api',
    },
  });
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;

    res.status(200).json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      services: {
        database: 'connected',
        api: 'running',
      },
      version: '1.0.0',
    });
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      services: {
        database: 'disconnected',
        api: 'running',
      },
      error: 'Database connection failed',
    });
  }
});

// Favicon handler
app.get('/favicon.ico', (req, res) => {
  res.status(204).end();
});

// Test endpoint
app.get('/test', (req, res) => {
  res.status(200).json({
    message: 'Server is working correctly!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: '1.0.0',
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', authMiddleware, auditMiddleware, userRoutes);
app.use('/api/companies', authMiddleware, auditMiddleware, companyRoutes);
app.use('/api/publications', authMiddleware, auditMiddleware, publicationRoutes);
app.use('/api/media-channels', authMiddleware, auditMiddleware, mediaChannelRoutes);
app.use('/api/data-parameters', authMiddleware, auditMiddleware, dataParameterRoutes);
app.use('/api/data-entries', authMiddleware, auditMiddleware, dataEntryRoutes);
app.use('/api/editorials', authMiddleware, auditMiddleware, editorialRoutes);
app.use('/api/swot-analysis', authMiddleware, auditMiddleware, swotAnalysisRoutes);
app.use('/api/daily-mentions', authMiddleware, auditMiddleware, dailyMentionRoutes);
app.use('/api/analytics', authMiddleware, analyticsRoutes);
app.use('/api/files', authMiddleware, auditMiddleware, fileUploadRoutes);
app.use('/api/audit-logs', authMiddleware, auditLogRoutes);
app.use('/api/export', authMiddleware, auditMiddleware, exportRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
});

export default app;
