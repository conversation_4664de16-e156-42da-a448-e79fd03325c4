
import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  <PERSON>alogTitle, 
  DialogTrigger,
  DialogDescription
} from '@/components/ui/dialog';
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from '@/components/ui/pagination';
import { Pencil, Trash2, Search, Plus, RefreshCw, Download, Upload } from 'lucide-react';
import { CreateCompanyForm } from '@/components/admin/CreateCompanyForm';
import { useCompanies, useDeleteCompany, useCompanySearch } from '@/hooks/useApi';
import { GlobalSearch } from '@/components/GlobalSearch';
import { FileUpload } from '@/components/FileUpload';
import { apiService } from '@/services/apiService';
import { toast } from 'sonner';

// Define the Company interface
interface Company {
  id: number;
  name: string;
  email: string;
  industry: string;
  ceo: string;
  phone: string;
  address: string;
}

// Mock data for companies
const mockCompanies: Company[] = [
  {
    id: 1,
    name: 'Brynn Stout',
    email: '<EMAIL>',
    industry: 'Ab id voluptatem i',
    ceo: 'Blanditiis',
    phone: '+****************',
    address: 'Quam quis sunt et il consequat'
  },
  {
    id: 2,
    name: 'testcompany',
    email: '<EMAIL>',
    industry: 'Test',
    ceo: 'Peter smith',
    phone: '7678765678',
    address: 'Ring road 67 wilson gate'
  },
  {
    id: 3,
    name: 'Toggle test comp',
    email: '<EMAIL>',
    industry: 'IT',
    ceo: 'Incididunt reprehend',
    phone: '+****************',
    address: 'Ea esse temporibus'
  },
  {
    id: 4,
    name: 'Alden Farley',
    email: '<EMAIL>',
    industry: 'Similique debitis se',
    ceo: 'A qui ut a sed eorum',
    phone: '+****************',
    address: 'Veniam maiores volu'
  },
  {
    id: 5,
    name: 'Jada Booth',
    email: '<EMAIL>',
    industry: 'Eaque esse voluptas',
    ceo: 'Nam ad molestiae inc',
    phone: '+****************',
    address: 'Sed blanditiis facer'
  },
  {
    id: 6,
    name: 'Bree Stark',
    email: '<EMAIL>',
    industry: 'Autem velit minim qu',
    ceo: 'Soluta enim dicta pa',
    phone: '+****************',
    address: 'Ullam quod deleniti'
  },
  {
    id: 7,
    name: 'AXA Mansard Insurance',
    email: '<EMAIL>',
    industry: 'Financial Services',
    ceo: 'Kunle Ahmed',
    phone: '**************',
    address: 'Lagos'
  },
  {
    id: 8,
    name: 'FCMB',
    email: '<EMAIL>',
    industry: 'Financial Services',
    ceo: 'Yemisi Edun',
    phone: '**************',
    address: 'Lagos'
  },
  {
    id: 9,
    name: 'Stanbic IBTC Bank',
    email: '<EMAIL>',
    industry: 'Financial Services',
    ceo: 'Demola Sogunle',
    phone: '0700 909 909 909',
    address: 'Lagos'
  },
  {
    id: 10,
    name: 'FIDELITY',
    email: '<EMAIL>',
    industry: 'Est vitae quaerat ac',
    ceo: 'Rem a quaerat a perf',
    phone: '+****************',
    address: 'Tenetur ea quia pari'
  }
];

const CompaniesPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const companiesPerPage = 10;

  // API hooks
  const { data: companies, loading, error, refetch } = useCompanies({
    page: currentPage,
    limit: companiesPerPage,
    search: searchTerm
  });
  const { mutate: deleteCompany, loading: deleting } = useDeleteCompany();

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // API handles filtering and pagination
  const currentCompanies = companies || [];
  const totalPages = Math.ceil((companies?.length || 0) / companiesPerPage);

  // Handle edit
  const handleEdit = (id: number) => {
    console.log('Edit company with id:', id);
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this company?')) return;

    try {
      await deleteCompany(id.toString());
      toast.success("Company deleted successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to delete company");
    }
  };

  // Handle save for new company
  const handleSaveCompany = (company: Company) => {
    setIsDialogOpen(false);
    toast.success("Company created successfully");
    refetch(); // Refresh the list
  };

  // Handle search result selection
  const handleSearchResult = (result: any) => {
    if (result.type === 'company') {
      toast.success(`Selected company: ${result.title}`);
    }
  };

  // Handle file upload
  const handleFileUpload = (files: any[]) => {
    toast.success(`Uploaded ${files.length} files successfully`);
    setIsUploadDialogOpen(false);
  };

  // Handle export
  const handleExport = async () => {
    try {
      const response = await apiService.exportCompanies({ format: 'csv' });
      toast.success('Companies exported successfully');
    } catch (error) {
      toast.error('Failed to export companies');
    }
  };

  // Handle cancel for company form
  const handleCancelCompany = () => {
    setIsDialogOpen(false);
  };

  // Generate page numbers for pagination
  const pageNumbers = [];
  for (let i = 1; i <= totalPages; i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="p-6 h-full">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Companies</h1>
          <GlobalSearch onResultSelect={handleSearchResult} className="mt-2" />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => refetch()} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                Upload
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Upload Company Data</DialogTitle>
              </DialogHeader>
              <FileUpload
                uploadType="data"
                accept=".csv,.xlsx,.xls"
                onUploadComplete={handleFileUpload}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600">Error loading companies: {error}</p>
        </div>
      )}

      <div className="flex justify-between mb-4">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search companies..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-indigo-950">
              <Plus className="mr-2 h-4 w-4" />
              Create Company
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[900px]">
            <DialogHeader>
              <DialogTitle>Create Company</DialogTitle>
              <DialogDescription>
                Fill in the details to create a new company. You can create up to 2 companies at once.
              </DialogDescription>
            </DialogHeader>
            <CreateCompanyForm 
              onSave={handleSaveCompany} 
              onCancel={handleCancelCompany} 
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-14">Sr.</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Industry</TableHead>
              <TableHead>CEO</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Office Address</TableHead>
              <TableHead className="text-right">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                  Loading companies...
                </TableCell>
              </TableRow>
            ) : currentCompanies.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  No companies found
                </TableCell>
              </TableRow>
            ) : (
              currentCompanies.map((company, index) => (
              <TableRow key={company.id}>
                <TableCell>{indexOfFirstCompany + index + 1}</TableCell>
                <TableCell>{company.name}</TableCell>
                <TableCell>{company.email}</TableCell>
                <TableCell>{company.industry}</TableCell>
                <TableCell>{company.ceo}</TableCell>
                <TableCell>{company.phone}</TableCell>
                <TableCell>{company.address}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEdit(company.id)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDelete(company.id)}
                      disabled={deleting}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="mt-4">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                />
              </PaginationItem>
              
              {pageNumbers.map(number => (
                <PaginationItem key={number}>
                  <PaginationLink
                    isActive={currentPage === number}
                    onClick={() => setCurrentPage(number)}
                  >
                    {number}
                  </PaginationLink>
                </PaginationItem>
              ))}
              
              <PaginationItem>
                <PaginationNext 
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      <div className="mt-4 text-sm text-gray-500">
        Showing {((currentPage - 1) * companiesPerPage) + 1} to {Math.min(currentPage * companiesPerPage, companies?.length || 0)} of {companies?.length || 0} results
      </div>
    </div>
  );
};

export default CompaniesPage;
