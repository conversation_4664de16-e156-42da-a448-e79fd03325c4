import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';
import { sendSuccess, sendError, sendPaginatedResponse } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';

export const getEditorials = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { 
    page = 1, 
    limit = 10, 
    search, 
    companyId, 
    publicationId, 
    mediaType,
    sentiment,
    status, 
    analystId,
    startDate,
    endDate,
    sortBy = 'createdAt', 
    sortOrder = 'desc' 
  } = req.query;

  const skip = (Number(page) - 1) * Number(limit);

  // Build where clause
  const where: any = {};

  if (companyId) {
    where.companyId = companyId;
  }

  if (publicationId) {
    where.publicationId = publicationId;
  }

  if (mediaType) {
    where.mediaType = mediaType;
  }

  if (sentiment) {
    where.sentiment = sentiment;
  }

  if (status) {
    where.status = status;
  }

  if (analystId) {
    where.analystId = analystId;
  }

  if (startDate || endDate) {
    where.date = {};
    if (startDate) {
      where.date.gte = new Date(startDate as string);
    }
    if (endDate) {
      where.date.lte = new Date(endDate as string);
    }
  }

  if (search) {
    where.OR = [
      { title: { contains: search as string, mode: 'insensitive' } },
      { brand: { contains: search as string, mode: 'insensitive' } },
      { reporter: { contains: search as string, mode: 'insensitive' } },
      { spokesperson: { contains: search as string, mode: 'insensitive' } },
      { activity: { contains: search as string, mode: 'insensitive' } },
      { company: { name: { contains: search as string, mode: 'insensitive' } } },
      { publication: { name: { contains: search as string, mode: 'insensitive' } } },
    ];
  }

  // Role-based filtering
  if (req.user!.role === 'ANALYST') {
    where.analystId = req.user!.id;
  }

  // Get total count
  const total = await prisma.editorial.count({ where });

  // Get editorials
  const editorials = await prisma.editorial.findMany({
    where,
    include: {
      company: {
        select: {
          id: true,
          name: true,
          industry: true,
        },
      },
      publication: {
        select: {
          id: true,
          name: true,
          type: true,
          country: true,
        },
      },
      analyst: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    skip,
    take: Number(limit),
    orderBy: {
      [sortBy as string]: sortOrder,
    },
  });

  return sendPaginatedResponse(res, editorials, {
    page: Number(page),
    limit: Number(limit),
    total,
  });
});

export const getEditorialById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const editorial = await prisma.editorial.findUnique({
    where: { id },
    include: {
      company: {
        select: {
          id: true,
          name: true,
          industry: true,
        },
      },
      publication: {
        select: {
          id: true,
          name: true,
          type: true,
          country: true,
        },
      },
      analyst: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  if (!editorial) {
    return sendError(res, 'Editorial not found', 404);
  }

  // Role-based access control
  if (req.user!.role === 'ANALYST' && editorial.analystId !== req.user!.id) {
    return sendError(res, 'Access denied', 403);
  }

  return sendSuccess(res, editorial, 'Editorial retrieved successfully');
});

export const createEditorial = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const {
    date,
    companyId,
    industry,
    brand,
    subSector,
    publicationId,
    placement,
    title,
    page,
    link,
    reporter,
    country = 'Nigeria',
    language = 'English',
    spokesperson,
    activity,
    mediaType,
    onlineChannel,
    sentiment,
    mediaSentimentIndex = 0,
    advertSpend = 0,
    circulation = 0,
    audienceReach = 0,
    pageSize,
    analystNote,
    supervisorNote,
    adminNote,
  } = req.body;

  // Verify that the referenced entities exist
  const [company, publication] = await Promise.all([
    prisma.company.findUnique({ where: { id: companyId } }),
    prisma.publication.findUnique({ where: { id: publicationId } }),
  ]);

  if (!company) {
    return sendError(res, 'Company not found', 400);
  }

  if (!publication) {
    return sendError(res, 'Publication not found', 400);
  }

  // Create editorial
  const editorial = await prisma.editorial.create({
    data: {
      date: new Date(date),
      companyId,
      industry,
      brand,
      subSector,
      publicationId,
      placement,
      title,
      page,
      link,
      reporter,
      country,
      language,
      spokesperson,
      activity,
      mediaType,
      onlineChannel,
      sentiment,
      mediaSentimentIndex,
      advertSpend,
      circulation,
      audienceReach,
      pageSize,
      analystNote,
      supervisorNote,
      adminNote,
      analystId: req.user!.id,
    },
    include: {
      company: {
        select: {
          id: true,
          name: true,
          industry: true,
        },
      },
      publication: {
        select: {
          id: true,
          name: true,
          type: true,
          country: true,
        },
      },
      analyst: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  return sendSuccess(res, editorial, 'Editorial created successfully', 201);
});

export const updateEditorial = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const updateData = req.body;

  // Check if editorial exists
  const existingEditorial = await prisma.editorial.findUnique({
    where: { id },
  });

  if (!existingEditorial) {
    return sendError(res, 'Editorial not found', 404);
  }

  // Role-based access control
  if (req.user!.role === 'ANALYST' && existingEditorial.analystId !== req.user!.id) {
    return sendError(res, 'Access denied', 403);
  }

  // Prepare update data based on user role
  const allowedFields: any = {};

  if (req.user!.role === 'ANALYST') {
    // Analysts can update most fields but not status or supervisor/admin notes
    const analystFields = [
      'date', 'companyId', 'industry', 'brand', 'subSector', 'publicationId',
      'placement', 'title', 'page', 'link', 'reporter', 'country', 'language',
      'spokesperson', 'activity', 'mediaType', 'onlineChannel', 'sentiment',
      'mediaSentimentIndex', 'advertSpend', 'circulation', 'audienceReach',
      'pageSize', 'analystNote'
    ];
    
    analystFields.forEach(field => {
      if (updateData[field] !== undefined) {
        allowedFields[field] = field === 'date' ? new Date(updateData[field]) : updateData[field];
      }
    });
  } else {
    // Supervisors and admins can update everything
    Object.keys(updateData).forEach(field => {
      if (field !== 'id' && field !== 'analystId' && field !== 'createdAt' && field !== 'updatedAt') {
        allowedFields[field] = field === 'date' ? new Date(updateData[field]) : updateData[field];
      }
    });
  }

  // Update editorial
  const editorial = await prisma.editorial.update({
    where: { id },
    data: allowedFields,
    include: {
      company: {
        select: {
          id: true,
          name: true,
          industry: true,
        },
      },
      publication: {
        select: {
          id: true,
          name: true,
          type: true,
          country: true,
        },
      },
      analyst: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  return sendSuccess(res, editorial, 'Editorial updated successfully');
});

export const deleteEditorial = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Check if editorial exists
  const existingEditorial = await prisma.editorial.findUnique({
    where: { id },
  });

  if (!existingEditorial) {
    return sendError(res, 'Editorial not found', 404);
  }

  // Role-based access control
  if (req.user!.role === 'ANALYST' && existingEditorial.analystId !== req.user!.id) {
    return sendError(res, 'Access denied', 403);
  }

  // Delete editorial
  await prisma.editorial.delete({
    where: { id },
  });

  return sendSuccess(res, null, 'Editorial deleted successfully');
});
