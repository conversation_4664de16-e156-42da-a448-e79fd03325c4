import { Router } from 'express';
import {
  getEditorials,
  getEditorialById,
  createEditorial,
  updateEditorial,
  deleteEditorial,
} from '../controllers/editorialController';
import { requireAnalystOrAbove } from '../middleware/auth';
import { validate, validateQuery } from '../utils/validation';
import {
  editorialCreateSchema,
  editorialUpdateSchema,
  paginationSchema,
  dateRangeSchema,
} from '../utils/validation';

const router = Router();

// Get all editorials (Analyst and above)
router.get('/', requireAnalystOrAbove, validateQuery(paginationSchema), getEditorials);

// Get editorial by ID (Analyst and above)
router.get('/:id', requireAnalystOrAbove, getEditorialById);

// Create editorial (Analyst and above)
router.post('/', requireAnalystOrAbove, validate(editorialCreateSchema), createEditorial);

// Update editorial (Analyst and above)
router.put('/:id', requireAnalystOrAbove, validate(editorialUpdateSchema), updateEditorial);

// Delete editorial (Analyst and above)
router.delete('/:id', requireAnalystOrAbove, deleteEditorial);

export default router;
