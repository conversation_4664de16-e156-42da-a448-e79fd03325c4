// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  SUPERVISOR
  ANALYST
  CLIENT
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum MediaType {
  PRINT
  ONLINE
  TELEVISION
  RADIO
  SOCIAL_MEDIA
}

enum SentimentType {
  POSITIVE
  NEGATIVE
  NEUTRAL
  NA
}

enum ContentStatus {
  PENDING
  APPROVED
  REJECTED
  DRAFT
}

enum PublicationType {
  PRINT
  ONLINE
  BOTH
}

model User {
  id              String     @id @default(cuid())
  email           String     @unique
  name            String
  password        String
  role            UserRole
  status          UserStatus @default(ACTIVE)
  avatar          String?
  mobileContact   String?
  countryCode     String?
  supervisorId    String?
  supervisor      User?      @relation("UserSupervisor", fields: [supervisorId], references: [id])
  subordinates    User[]     @relation("UserSupervisor")
  expirationDate  DateTime?
  lastLogin       DateTime?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  dataEntries     DataEntry[]
  editorials      Editorial[]
  swotAnalyses    SwotAnalysis[]
  dailyMentions   DailyMention[]
  auditLogs       AuditLog[]
  createdCompanies Company[] @relation("CompanyCreatedBy")
  createdPublications Publication[] @relation("PublicationCreatedBy")

  @@map("users")
}

model Company {
  id          String   @id @default(cuid())
  name        String   @unique
  industry    String
  website     String?
  logo        String?
  description String?
  isActive    Boolean  @default(true)
  createdById String
  createdBy   User     @relation("CompanyCreatedBy", fields: [createdById], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  dataEntries   DataEntry[]
  editorials    Editorial[]
  swotAnalyses  SwotAnalysis[]
  dailyMentions DailyMention[]

  @@map("companies")
}

model Publication {
  id          String          @id @default(cuid())
  name        String          @unique
  type        PublicationType
  website     String?
  country     String          @default("Nigeria")
  language    String          @default("English")
  circulation Int             @default(0)
  isActive    Boolean         @default(true)
  createdById String
  createdBy   User            @relation("PublicationCreatedBy", fields: [createdById], references: [id])
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relations
  editorials Editorial[]

  @@map("publications")
}

model MediaChannel {
  id          String    @id @default(cuid())
  name        String    @unique
  type        MediaType
  category    String
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  dataEntries DataEntry[]

  @@map("media_channels")
}

model DataParameter {
  id          String @id @default(cuid())
  name        String @unique
  category    String
  description String?
  unit        String?
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  dataEntries DataEntry[]

  @@map("data_parameters")
}

model DataEntry {
  id            String        @id @default(cuid())
  companyId     String
  company       Company       @relation(fields: [companyId], references: [id])
  parameterId   String
  parameter     DataParameter @relation(fields: [parameterId], references: [id])
  channelId     String
  channel       MediaChannel  @relation(fields: [channelId], references: [id])
  value         Float
  date          DateTime
  analystId     String
  analyst       User          @relation(fields: [analystId], references: [id])
  status        ContentStatus @default(PENDING)
  comments      String?
  metadata      Json?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@map("data_entries")
}

model Editorial {
  id                   String        @id @default(cuid())
  date                 DateTime
  companyId            String
  company              Company       @relation(fields: [companyId], references: [id])
  industry             String
  brand                String
  subSector            String?
  publicationId        String
  publication          Publication   @relation(fields: [publicationId], references: [id])
  placement            String?
  title                String
  page                 String?
  link                 String?
  reporter             String?
  country              String        @default("Nigeria")
  language             String        @default("English")
  spokesperson         String?
  activity             String?
  mediaType            MediaType
  onlineChannel        String?
  sentiment            SentimentType
  mediaSentimentIndex  Float         @default(0)
  advertSpend          Float         @default(0)
  circulation          Int           @default(0)
  audienceReach        Int           @default(0)
  pageSize             String?
  analystNote          String?
  supervisorNote       String?
  adminNote            String?
  status               ContentStatus @default(PENDING)
  analystId            String
  analyst              User          @relation(fields: [analystId], references: [id])
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt

  @@map("editorials")
}

model SwotItem {
  id           String       @id @default(cuid())
  content      String
  type         String       // STRENGTH, WEAKNESS, OPPORTUNITY, THREAT
  swotAnalysisId String
  swotAnalysis SwotAnalysis @relation(fields: [swotAnalysisId], references: [id], onDelete: Cascade)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  @@map("swot_items")
}

model SwotAnalysis {
  id             String     @id @default(cuid())
  companyId      String
  company        Company    @relation(fields: [companyId], references: [id])
  date           DateTime
  analystNote    String?
  supervisorNote String?
  status         ContentStatus @default(PENDING)
  analystId      String
  analyst        User       @relation(fields: [analystId], references: [id])
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  // Relations
  items SwotItem[]

  @@map("swot_analyses")
}

model DailyMentionHighlight {
  id             String       @id @default(cuid())
  content        String
  type           SentimentType
  dailyMentionId String
  dailyMention   DailyMention @relation(fields: [dailyMentionId], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@map("daily_mention_highlights")
}

model DailyMention {
  id             String                  @id @default(cuid())
  date           DateTime
  companyId      String
  company        Company                 @relation(fields: [companyId], references: [id])
  title          String
  publications   String[]
  analystNote    String?
  supervisorNote String?
  status         ContentStatus           @default(PENDING)
  analystId      String
  analyst        User                    @relation(fields: [analystId], references: [id])
  createdAt      DateTime                @default(now())
  updatedAt      DateTime                @updatedAt

  // Relations
  highlights DailyMentionHighlight[]

  @@map("daily_mentions")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  action    String
  resource  String
  resourceId String?
  details   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  @@map("audit_logs")
}

model FileUpload {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  mimetype     String
  size         Int
  path         String
  uploadedBy   String?
  metadata     Json?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("file_uploads")
}
