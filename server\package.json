{"name": "media-monitoring-backend", "version": "1.0.0", "description": "Backend API for Media Monitoring Dashboard", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx src/scripts/seed.ts", "db:setup": "tsx src/scripts/setup.ts", "db:studio": "prisma studio", "test": "jest", "test:watch": "jest --watch", "test:api": "tsx src/scripts/test-api.ts", "check": "node startup-check.js"}, "dependencies": {"@prisma/client": "^6.8.2", "@prisma/extension-accelerate": "^2.0.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^6.8.2", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["media-monitoring", "analytics", "dashboard", "api", "express", "typescript"], "author": "Media Monitoring Team", "license": "MIT"}