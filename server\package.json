{"name": "media-monitoring-backend", "version": "1.0.0", "description": "Backend API for Media Monitoring Dashboard", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx src/scripts/seed.ts", "db:studio": "prisma studio", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "express-validator": "^7.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "date-fns": "^3.6.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/compression": "^1.7.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@types/node": "^20.10.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "tsx": "^4.6.2", "prisma": "^5.7.1", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["media-monitoring", "analytics", "dashboard", "api", "express", "typescript"], "author": "Media Monitoring Team", "license": "MIT"}